// Comprehensive TypeScript interfaces for Liden API

// ============================================================================
// COMMON TYPES
// ============================================================================

export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
}

export interface PaginationParams {
  sort?: string;
  offset?: number;
  limit?: number;
  [key: string]: string | number | undefined;
}

export interface DateRangeParams {
  start?: string;
  end?: string;
  [key: string]: string | number | undefined;
}

export interface ExportParams {
  export?: boolean | number;
  [key: string]: string | number | boolean | undefined;
}

// ============================================================================
// AUTHENTICATION TYPES
// ============================================================================

export interface LoginRequest {
  userName: string;
  countryCode: string;
  password: string;
  apigw: string;
}

export interface LoginResponse {
  code: string;
  statusDescription: string;
  data: {
    code: number;
    message: string;
    data: {
      token: string;
      client_data: string;
      expires: number;
      type: string;
    };
  };
}

export interface ForgotPasswordRequest {
  phoneNumber: string;
  countryCode: string;
}

export interface ResetPasswordRequest {
  token: string;
  newPassword: string;
}

export interface UserProfile {
  id: string;
  phoneNumber: string;
  countryCode: string;
  name: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  role?: string;
}

// ============================================================================
// SMS TYPES
// ============================================================================

export interface SMSBlastRequest {
  shortCode: string;
  message: string;
  approval?: string;
  queryRecipient?: string;
  blast_name: string;
  uniqueId: string;
  isScheduled?: string;
  scheduleDate?: string;
  scheduleTime?: string;
  callbackURL?: string;
}

export interface SMSMessage {
  id: string;
  message: string;
  recipient: string;
  status: string;
  timestamp: string;
  shortCode?: string;
}

export interface SMSAnalytics {
  totalSent: number;
  totalDelivered: number;
  totalFailed: number;
  deliveryRate: number;
}

// ============================================================================
// SURVEY TYPES
// ============================================================================

export interface SurveyCreateRequest {
  survey: {
    surveyName: string;
    surveyDesc: string;
    contactLists: string;
    surveyMode: {
      channelMode: string;
      channelSource: string;
    };
    autoReminder?: string;
    enableSavePoint?: string;
    enableWhitelist?: string;
    reminderPeriod?: string;
    optinMessage?: string;
    optoutMessage?: string;
    reminderMessage?: string;
    countryCode: string;
    startDate: string;
    stopDate: string;
  };
  questionnaire: {
    questionnareName: string;
    questionnareDesc: string;
    questionnareQuestions: QuestionnaireQuestion[];
  };
  incentive: {
    incentiveType: string;
    utilityId: string;
    rewardAmount: string;
    rewardName: string;
    mechanicLimit: string;
  };
}

export interface QuestionnaireQuestion {
  question: string;
  questionTypeId: string;
  questionChoices: QuestionChoice[];
}

export interface QuestionChoice {
  value: string;
  numbering: string;
  linkQuestionId?: string;
}

export interface SurveyResponse {
  id: string;
  surveyId: string;
  msisdn: string;
  responses: any[];
  timestamp: string;
}

export interface SurveyApplication {
  id: string;
  surveyName: string;
  status: string;
  clientId: string;
  createdDate: string;
}

// ============================================================================
// UTILITY/AIRTIME TYPES
// ============================================================================

export interface AirtimeSingleRequest {
  recipient: string;
  amount: string;
  currency: string;
  uniqueId: string;
  retry?: string;
  dialCode: string;
  callbackURL?: string;
}

export interface AirtimeBulkRequest {
  clientId?: string;
  countryCode: string;
  callbackUrl?: string;
  uniqueId: string;
  uploadedFile: File;
}

export interface MpesaPayoutRequest {
  amount: string;
  callback: string;
  merchantId: string;
  msisdn: string;
  narration: string;
  uniqueId: string | number;
}

export interface MpesaB2BPayoutRequest extends MpesaPayoutRequest {
  ReceivingPaybillNumber: string;
  accountNumber: string;
}

export interface UtilityTransaction {
  id: string;
  amount: string;
  recipient: string;
  status: string;
  statusDesc: string;
  timestamp: string;
  uniqueId: string;
}

// ============================================================================
// USSD TYPES
// ============================================================================

export interface USSDGatewayParams {
  msisdn: string;
  ussd_string: string;
  service_code: string;
  session_id: string;
  [key: string]: string | number;
}

export interface USSDApp {
  id: string;
  clientId: string;
  systemName: string;
  status: string;
}

export interface USSDAccessPoint {
  id: string;
  appId: string;
  serviceCode: string;
  systemName: string;
  status: string;
}

export interface USSDCreateAppRequest {
  clientId: string;
  systemName: string;
}

export interface USSDConfigureAccessPointRequest {
  appId: string;
  typeId: string;
  accessPoint: string;
  networkId: string;
  defaultMessage: string;
  callbackUrl: string;
  callbackPortNumber: string;
}

// Enhanced USSD parameter types with date filtering
export interface USSDAppsParams extends PaginationParams, DateRangeParams, ExportParams {
  clientId?: string;
  serviceCode?: string;
  systemName?: string;
  status?: string;
}

export interface USSDTypesParams extends DateRangeParams {
  status?: string;
  clientId?: string;
}

export interface USSDAccessPointsParams extends PaginationParams, DateRangeParams, ExportParams {
  clientId?: string;
  serviceCode?: string;
  systemName?: string;
  status?: string;
}

// SMS Networks types - Updated to match actual API response
export interface SMSNetwork {
  network_id: string;
  network_name: string;
  network_code: string;
  country_code: string;
  country: string;
  sms_rate: string;
  minimum_rate: string;
  is_local: string;
  sender_id_cost: string;
  short_code_cost: string;
}

export interface SMSNetworksResponse {
  total_count: number;
  data: SMSNetwork[];
}

export interface SMSNetworksParams extends PaginationParams, DateRangeParams {
  limit?: number;
  status?: string;
  country?: string;
}

// ============================================================================
// SENDER IDS TYPES
// ============================================================================

export interface SenderIdRequest {
  total_count: string;
  id: string;
  sender_id: string;
  sender_type: string;
  sStatus: string;
  short_code: string;
  status: string;
  client_id: string;
  client_name: string;
  client_email: string;
}

export interface SenderIdsResponse {
  total_count: string;
  data: SenderIdRequest[];
}

export interface SenderIdsParams extends PaginationParams, DateRangeParams, ExportParams {
  shortCode?: string;
  typeId?: string;
  status?: string;
  clientId?: string;
  sort?: string;
}

// ============================================================================
// USSD TYPES
// ============================================================================

export interface USSDApp {
  total_count: string;
  id: string;
  c_serviceId: string;
  system_name: string;
  status: string;
  created_at: string;
  created_by: string;
  approved_by: string | null;
}

export interface USSDAppsResponse {
  total_count: string;
  data: USSDApp[];
}

export interface USSDType {
  type_id: string;
  name: string;
  description: string;
}

export interface USSDTypesResponse {
  data: USSDType[];
}

export interface USSDAppsParams extends PaginationParams, DateRangeParams, ExportParams {
  clientId?: string;
  serviceCode?: string;
  systemName?: string;
  status?: string;
}

export interface USSDTypesParams extends DateRangeParams {
  status?: string;
  clientId?: string;
}

// ============================================================================
// CONTACT TYPES
// ============================================================================

export interface Contact {
  id: string;
  msisdn: string;
  listId: string;
  custom1?: string;
  custom2?: string;
  custom3?: string;
  custom4?: string;
  custom5?: string;
}

export interface ContactEditRequest {
  status?: string;
  custom1?: string;
  custom2?: string;
  custom3?: string;
  custom4?: string;
  custom5?: string;
}

export interface ContactListParams extends PaginationParams, DateRangeParams {
  msisdn?: string;
  listId?: string;
}

// ============================================================================
// CLIENT MANAGEMENT TYPES
// ============================================================================

export interface AddUserRequest {
  mobile: string;
  countryCode: string;
  emailAddress: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  roleId: string;
  permissionAcl?: string;
  clientId?: string;
}

export interface EditUserRequest {
  userMapId: string;
  roleId?: string;
  emailAddress?: string;
  callbackUrl?: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  surName?: string;
  status?: string;
}

export interface UserPermissionsRequest {
  permissionAcl: string;
  user_mapId: string;
}

export interface MpesaConfigRequest {
  paybillNumber: string;
  orgName: string;
  maxPayouts: string;
  callbackUrl: string;
  mpesaUsername: string;
  mpesaPass: string;
  consumerKey: string;
  secretKey: string;
  clientId: string;
  ipWhitelist: string;
}

export interface ClientWallet {
  balance: number;
  currency: string;
  clientId: string;
}

export interface AuditLog {
  id: string;
  userId: string;
  action: string;
  timestamp: string;
  details: string;
}

export interface Invoice {
  id: string;
  invoiceId: string;
  amount: number;
  clientId: string;
  serviceId: string;
  status: string;
  createdDate: string;
}

// ============================================================================
// DASHBOARD TYPES
// ============================================================================

export interface DashboardStats {
  totalSMS: number;
  totalSurveys: number;
  totalAirtime: number;
  totalRevenue: number;
  period: {
    start: string;
    end: string;
  };
}

// ============================================================================
// WALLET TYPES
// ============================================================================

export interface WalletResponse {
  code: string;
  statusDescription: string;
  data: {
    code: number;
    message: string;
    data: {
      client_id: string;
      billing_id: string;
      alert_threshold: string;
      client_name: string;
      client_email: string;
      address: string;
      can_resell: string;
      status: string;
      country: string;
      country_code: string;
      accountId: string;
      balance: string;
      bonus: string;
      currency: string;
      subscription_balance: string;
      maximum_credit_amount: string;
    };
  };
}

// ============================================================================
// BULK SMS USAGE TYPES
// ============================================================================

export interface BulkUsageResponse {
  code: string;
  statusDescription: string;
  data: {
    code: number;
    message: string;
    data: {
      total_count: string;
      data: Array<{
        count: string;
        campaign_id: string;
        short_code: string;
        campaign_name: string;
        message: string;
        recipients: string;
        sent: string;
        delivered: string;
        failed: string;
        pending: string;
        delivery_percent: string;
        origin: string;
        cost_incurred: string;
        status: string;
        sms_pages: string;
        is_scheduled: string;
        send_time: string;
        completed_on: string;
        created_at: string;
        created_by: string;
        creator: string;
      }>;
    };
  };
}

export interface BulkUsageParams extends PaginationParams {
  limit?: number;
  status?: number;
  clientId?: string;
  startDate?: string;
  endDate?: string;
}

// ============================================================================
// DASHBOARD STATISTICS TYPES
// ============================================================================

export interface DashboardStatsResponse {
  code: string;
  statusDescription: string;
  data: {
    code: number;
    message: string;
    data: {
      sms_sent: string;
      delivery_report: {
        sms_recipients: string;
        sms_delivered: string;
        sms_failed: string;
        sms_pending: string;
      };
      contacts: string;
    };
  };
}

// ============================================================================
// BULK MESSAGES TYPES
// ============================================================================

export interface BulkMessage {
  count: string;
  outbox_id: string;
  msisdn: string;
  short_code: string;
  message: string;
  message_length: string;
  sms_cost: string;
  alert_type: string;
  network: string;
  description: string;
  created_at: string;
}

export interface BulkMessagesResponse {
  total_count: string;
  data: BulkMessage[];
}

export interface BulkMessagesParams extends PaginationParams, DateRangeParams {
  search?: string;
  network?: string;
  type?: string;
  status?: string;
  sort?: string;
  order?: 'asc' | 'desc';
}

// ============================================================================
// CONTACT GROUPS TYPES
// ============================================================================

export interface ContactGroup {
  total_count: string;
  list_id: string;
  group_name: string;
  description: string;
  status: string;
  can_upload: string;
  custom1: string;
  custom2: string;
  custom3: string;
  custom4: string;
  custom5: string;
  deleted_on: string | null;
  created_at: string;
  created_by: string;
}

export interface ContactGroupsResponse {
  total_count: string;
  data: ContactGroup[];
}

export interface ContactGroupsParams extends PaginationParams {
  status?: string;
  search?: string;
}

// ============================================================================
// COUNTRIES TYPES
// ============================================================================

export interface Country {
  country_id: string;
  country_name: string;
  country_code: string;
  dial_code: string;
  currency: string;
  currency_symbol: string;
}

export interface CountriesResponse {
  data: Country[];
}

// ============================================================================
// SENDER IDS TYPES
// ============================================================================

export interface SenderId {
  total_count: string;
  id: string;
  sender_id: string;
  sender_type: string;
  sStatus: string;
  short_code: string;
  status: string;
  client_id: string;
  client_name: string;
  client_email: string;
}

export interface SenderIdsResponse {
  total_count: string;
  data: SenderId[];
}

export interface SenderIdsParams extends PaginationParams, DateRangeParams {
  shortCode?: string;
  typeId?: string;
  status?: string;
  clientId?: string;
  export?:  number;
  sort?: string;
}

export interface DashboardStatsParams extends DateRangeParams {
  clientId?: string;
  campaignId?: string;
  groupBy?: 'day' | 'week' | 'month';
}

// ============================================================================
// SCHEDULER TYPES
// ============================================================================

export interface SchedulerExecuteRequest {
  serviceId: string;
  api_token: string;
  typeId?: string;
  productId?: string;
  start?: string;
  end?: string;
}

// ============================================================================
// WEBHOOK TYPES
// ============================================================================

export interface ATCallbackData {
  // AT callback validation data structure
  [key: string]: any;
}

export interface C2BConfirmationData {
  // C2B confirmation data structure
  [key: string]: any;
}

// ============================================================================
// PREMIUM CONTENT TYPES
// ============================================================================

export interface MessageReport {
  id: string;
  productId: string;
  shortCode: string;
  campaignId: string;
  reportId: string;
  timestamp: string;
}

export interface MessageReportParams extends PaginationParams, DateRangeParams {
  productId?: string;
  shortCode?: string;
  clientId?: string;
  campaignId?: string;
  reportId?: string;
}

// ============================================================================
// VOICE TYPES
// ============================================================================

export interface VoiceType {
  id: string;
  name: string;
  description: string;
}

// ============================================================================
// BULK RATE CARD TYPES
// ============================================================================

export interface BulkRateCard {
  id: string;
  service_id: string;
  service_name: string;
  discount: string;
  status: string;
  currency: string;
  network_id: string;
  network_name: string;
  network_code: string;
  country_code: string;
  unit_cost: string;
  country: string;
  is_local: string;
  sender_id_cost: string;
  short_code_cost: string;
  created_at: string;
}

export interface BulkRateCardResponse {
  total_count: number;
  data: BulkRateCard[];
}

export interface BulkRateCardParams extends PaginationParams, DateRangeParams {
  service_id?: string;
  network_id?: string;
  country_code?: string;
  status?: string;
}

// ============================================================================
// INVOICE TYPES
// ============================================================================

export interface Invoice {
  total_count: string;
  invoiceId: string;
  reference_number: string;
  service_id: string;
  service_name: string;
  amount: string;
  vat_tax_amount: string;
  currency: string;
  description: string;
  status: string;
  status_description: string;
  client_id: string;
  client_name: string;
  client_email: string;
  due_date: string;
  invoice_state: string;
  closed_on: string | null;
  created: string;
  created_by: string;
  full_names: string;
}

export interface InvoiceResponse {
  total_count: string;
  data: Invoice[];
}

export interface InvoiceParams extends PaginationParams, DateRangeParams {
  service_id?: string;
  client_id?: string;
  status?: string;
  invoice_state?: string;
}

// ============================================================================
// USER MANAGEMENT TYPES
// ============================================================================

export interface User {
  total_count: string;
  user_id: string;
  user_mapId: string;
  full_names: string;
  email_address: string;
  msisdn: string;
  first_name: string | null;
  middle_name: string | null;
  sur_name: string | null;
  network: string;
  role_id: string;
  role_name: string;
  client_name: string;
  account_status: string;
  reset_attempts: string;
  successful_attempts: string;
  failed_attempts: string;
  cumlative_failed_attempts: string;
  last_successful_date: string | null;
  last_failed_attempt: string | null;
  allocationId: string | null;
  currency: string | null;
  balance: string | null;
  bonus: string | null;
  allocation_status: string | null;
  blocked_timeline: string | null;
  activation_date: string;
  apikey_expiry_date: string;
  last_login_date: string | null;
  reset_expiry_date: string | null;
  created_at: string;
}

export interface UserRole {
  total_count: string;
  role_id: string;
  role_name: string;
  role_description: string;
}

export interface UserResponse {
  total_count: string;
  data: User[];
}

export interface UserRoleResponse {
  total_count: string;
  data: UserRole[];
}

export interface UserParams extends PaginationParams {
  role_id?: string;
  account_status?: string;
  client_id?: string;
}

export interface CreateUserRequest {
  first_name: string;
  middle_name?: string;
  sur_name: string;
  email_address: string;
  msisdn: string;
  country_code: string;
  role_id: string;
}

export interface UpdateUserRequest {
  user_id: string;
  first_name?: string;
  middle_name?: string;
  sur_name?: string;
  email_address?: string;
  msisdn?: string;
  role_id?: string;
  account_status?: string;
}
