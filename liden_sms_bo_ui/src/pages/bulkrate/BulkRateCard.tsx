import { useState, useEffect } from "react"
import { Search, Filter, Download, RefreshCw } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DateRangePicker } from "@/components/DateRangePicker"
import { DataTable, Column } from "@/components/DataTable"
import { LidenAPI } from "@/lib/api-index"
import { BulkRateCard, BulkRateCardParams } from "@/lib/api-types"
import { LoadingSpinner } from "@/components/LoadingSpinner"

export default function BulkRateCardPage() {
  const [rateCards, setRateCards] = useState<BulkRateCard[]>([])
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedService, setSelectedService] = useState<string>("")
  const [selectedNetwork, setSelectedNetwork] = useState<string>("")
  const [selectedCountry, setSelectedCountry] = useState<string>("")
  const [selectedStatus, setSelectedStatus] = useState<string>("")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalCount, setTotalCount] = useState(0)
  const pageSize = 20

  // Fetch bulk rate cards from API
  const fetchRateCards = async () => {
    try {
      setLoading(true)
      const offset = ((currentPage - 1) * pageSize) + 1

      const params: BulkRateCardParams = {
        limit: pageSize,
        offset: offset,
        service_id: selectedService || undefined,
        network_id: selectedNetwork || undefined,
        country_code: selectedCountry || undefined,
        status: selectedStatus || undefined,
      }

      const response = await LidenAPI.bulkRateCard.getBulkRateCards(params)

      if (response.success && response.data) {
        const rateCardData = response.data.data || []
        setRateCards(rateCardData)
        setTotalCount(response.data.total_count || 0)
      } else {
        setRateCards([])
        setTotalCount(0)
      }
    } catch (error) {
      console.error("Failed to fetch bulk rate cards:", error)
      setRateCards([])
      setTotalCount(0)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchRateCards()
  }, [currentPage, selectedService, selectedNetwork, selectedCountry, selectedStatus])

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    setCurrentPage(1)
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handleRefresh = () => {
    fetchRateCards()
  }

  const handleExport = () => {
    // TODO: Implement export functionality
    console.log("Export rate cards")
  }

  // Filter rate cards based on search query
  const filteredRateCards = rateCards.filter(card =>
    card.service_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    card.network_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    card.country.toLowerCase().includes(searchQuery.toLowerCase()) ||
    card.network_code.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const columns: Column<BulkRateCard>[] = [
    {
      key: "service_name",
      header: "Service",
      render: (card) => (
        <div className="font-medium">
          {card.service_name}
          <div className="text-sm text-muted-foreground">ID: {card.service_id}</div>
        </div>
      ),
    },
    {
      key: "network_name",
      header: "Network",
      render: (card) => (
        <div>
          <div className="font-medium">{card.network_name}</div>
          <div className="text-sm text-muted-foreground">{card.network_code}</div>
        </div>
      ),
    },
    {
      key: "country",
      header: "Country",
      render: (card) => (
        <div>
          <div className="font-medium">{card.country}</div>
          <div className="text-sm text-muted-foreground">+{card.country_code}</div>
        </div>
      ),
    },
    {
      key: "unit_cost",
      header: "Unit Cost",
      render: (card) => (
        <div className="text-right">
          <div className="font-medium">{card.currency} {parseFloat(card.unit_cost).toFixed(4)}</div>
          {parseFloat(card.discount) > 0 && (
            <div className="text-sm text-green-600">-{card.discount}% discount</div>
          )}
        </div>
      ),
    },
    {
      key: "sender_id_cost",
      header: "Sender ID Cost",
      render: (card) => (
        <div className="text-right font-medium">
          {card.currency} {parseFloat(card.sender_id_cost).toLocaleString()}
        </div>
      ),
    },
    {
      key: "short_code_cost",
      header: "Short Code Cost",
      render: (card) => (
        <div className="text-right font-medium">
          {card.currency} {parseFloat(card.short_code_cost).toLocaleString()}
        </div>
      ),
    },
    {
      key: "is_local",
      header: "Type",
      render: (card) => (
        <Badge variant={card.is_local === "Local" ? "default" : "secondary"}>
          {card.is_local}
        </Badge>
      ),
    },
    {
      key: "status",
      header: "Status",
      render: (card) => (
        <Badge variant={card.status === "1" ? "default" : "destructive"}>
          {card.status === "1" ? "Active" : "Inactive"}
        </Badge>
      ),
    },
    {
      key: "created_at",
      header: "Created",
      render: (card) => (
        <div className="text-sm">
          {new Date(card.created_at).toLocaleDateString()}
        </div>
      ),
    },
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Bulk Rate Card</h1>
          <p className="text-muted-foreground">
            Manage SMS service rate cards across different networks and countries
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleRefresh} disabled={loading}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex flex-1 items-center gap-2">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search services, networks, countries..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-9"
            />
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Select value={selectedService} onValueChange={setSelectedService}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Service" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Services</SelectItem>
              <SelectItem value="1">Bulk SMS Service</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedNetwork} onValueChange={setSelectedNetwork}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Network" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Networks</SelectItem>
              <SelectItem value="1">SAFARICOM</SelectItem>
              <SelectItem value="2">AIRTEL_KE</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedCountry} onValueChange={setSelectedCountry}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Country" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Countries</SelectItem>
              <SelectItem value="254">Kenya</SelectItem>
              <SelectItem value="256">Uganda</SelectItem>
              <SelectItem value="255">Tanzania</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedStatus} onValueChange={setSelectedStatus}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Status</SelectItem>
              <SelectItem value="1">Active</SelectItem>
              <SelectItem value="0">Inactive</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Data Table */}
      {loading ? (
        <div className="flex items-center justify-center py-8">
          <LoadingSpinner />
        </div>
      ) : (
        <DataTable
          data={filteredRateCards}
          columns={columns}
          currentPage={currentPage}
          totalPages={Math.ceil(totalCount / pageSize)}
          onPageChange={handlePageChange}
          loading={loading}
        />
      )}
    </div>
  )
}
