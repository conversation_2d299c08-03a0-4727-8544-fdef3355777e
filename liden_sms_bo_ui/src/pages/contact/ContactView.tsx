import { useState, useEffect } from "react"
import { MoreHorizontal, UserPlus, Upload, Download, Search, Plus, Edit, Trash2 } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DateRangePicker } from "@/components/DateRangePicker"
import { DataTable, Column } from "@/components/DataTable"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { LidenAPI } from "@/lib/api-index"
import { Contact, ContactGroup, Country } from "@/lib/api-types"

// Interface for contact with additional fields for display
interface ContactDisplay extends Contact {
  group_name?: string;
  status_display?: string;
}

const columns: Column[] = [
  {
    key: "msisdn",
    label: "Phone",
    sortable: true,
    render: (value: string) => (
      <div className="font-medium">
        {value}
      </div>
    ),
  },
  {
    key: "custom1",
    label: "Custom1",
    sortable: true,
    render: (value: string) => value || "-",
  },
  {
    key: "custom2",
    label: "Custom2",
    sortable: true,
    render: (value: string) => value || "-",
  },
  {
    key: "custom3",
    label: "Custom3",
    sortable: true,
    render: (value: string) => value || "-",
  },
  {
    key: "custom4",
    label: "Custom4",
    sortable: true,
    render: (value: string) => value || "-",
  },
  {
    key: "status_display",
    label: "Status",
    render: (value: string) => (
      <Badge variant="default" className="bg-green-600">
        {value || "Active"}
      </Badge>
    ),
  },
  {
    key: "created",
    label: "Created",
    sortable: true,
  },
  {
    key: "actions",
    label: "Actions",
    render: (_, row) => (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem>Edit</DropdownMenuItem>
          <DropdownMenuItem>Delete</DropdownMenuItem>
          <DropdownMenuItem>View Details</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    ),
  },
]

export function ContactView() {
  const [dateRange, setDateRange] = useState<any>(undefined)
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [groupFilter, setGroupFilter] = useState<string>("all")
  const [contacts, setContacts] = useState<ContactDisplay[]>([])
  const [contactGroups, setContactGroups] = useState<ContactGroup[]>([])
  const [countries, setCountries] = useState<Country[]>([])
  const [loading, setLoading] = useState(false)
  const [totalCount, setTotalCount] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedListId, setSelectedListId] = useState<string>("")

  // Fetch contacts from API
  const fetchContacts = async () => {
    if (!selectedListId) return

    try {
      setLoading(true)
      const offset = ((currentPage - 1) * pageSize) + 1

      const params = {
        listId: selectedListId,
        limit: pageSize,
        offset: offset,
        msisdn: searchQuery || undefined,
      }

      const response = await LidenAPI.contact.getContacts(params)

      if (response.success && response.data) {
        const contactsData = Array.isArray(response.data) ? response.data : []
        setContacts(contactsData)
        // Note: API might not return total_count, so we'll use data length for now
        setTotalCount(contactsData.length || 0)
      } else {
        setContacts([])
        setTotalCount(0)
      }
    } catch (error) {
      console.error("Failed to fetch contacts:", error)
      setContacts([])
      setTotalCount(0)
    } finally {
      setLoading(false)
    }
  }

  // Fetch contact groups
  const fetchContactGroups = async () => {
    try {
      const response = await LidenAPI.contact.getGroups({ limit: 100 })

      if (response.success && response.data) {
        let groupsData: any[] = []

        // Handle different possible response structures
        if (response.data?.data?.data && Array.isArray(response.data.data.data)) {
          groupsData = response.data.data.data
        } else if (response.data?.data && Array.isArray(response.data.data)) {
          groupsData = response.data.data
        } else if (Array.isArray(response.data)) {
          groupsData = response.data
        }

        setContactGroups(groupsData)

        // Set first group as default if none selected
        if (!selectedListId && groupsData.length > 0) {
          setSelectedListId(groupsData[0].list_id)
        }
      } else {
        setContactGroups([])
      }
    } catch (error) {
      console.error("Failed to fetch contact groups:", error)
      setContactGroups([])
    }
  }

  // Fetch countries
  const fetchCountries = async () => {
    try {
      const response = await LidenAPI.client.getCountries()

      // Handle different possible response structures
      if (response.success && response.data) {
        let countriesData: any[] = []

        if (response.data?.data?.data && Array.isArray(response.data.data.data)) {
          countriesData = response.data.data.data
        } else if (response.data?.data && Array.isArray(response.data.data)) {
          countriesData = response.data.data
        } else if (Array.isArray(response.data)) {
          countriesData = response.data
        }

        setCountries(countriesData)
      } else {
        setCountries([])
      }
    } catch (error) {
      console.error("Failed to fetch countries:", error)
      setCountries([])
    }
  }

  useEffect(() => {
    fetchContactGroups()
    fetchCountries()
  }, [])

  useEffect(() => {
    if (selectedListId) {
      fetchContacts()
    }
  }, [selectedListId, currentPage, pageSize, searchQuery])

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    setCurrentPage(1)
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handlePageSizeChange = (size: number) => {
    setPageSize(size)
    setCurrentPage(1)
  }

  const handleExport = (format: string) => {
    console.log("Export:", format)
  }

  const mobileCardRender = (contact: ContactDisplay) => (
    <div className="space-y-3 p-1">
      <div className="flex justify-between items-start">
        <div className="flex-1 min-w-0">
          <div className="grid grid-cols-1 gap-2 text-sm">
            <div className="flex justify-between items-center border-b border-border pb-2">
              <span className="font-medium text-muted-foreground">Phone</span>
              <span className="font-medium text-foreground text-right">{contact.msisdn}</span>
            </div>
            <div className="flex justify-between items-center border-b border-border pb-2">
              <span className="font-medium text-muted-foreground">Custom1</span>
              <span className="text-foreground text-right">{contact.custom1 || "-"}</span>
            </div>
            <div className="flex justify-between items-center border-b border-border pb-2">
              <span className="font-medium text-muted-foreground">Custom2</span>
              <span className="text-foreground text-right">{contact.custom2 || "-"}</span>
            </div>
            <div className="flex justify-between items-center border-b border-border pb-2">
              <span className="font-medium text-muted-foreground">Custom3</span>
              <span className="text-foreground text-right">{contact.custom3 || "-"}</span>
            </div>
            <div className="flex justify-between items-center border-b border-border pb-2">
              <span className="font-medium text-muted-foreground">Status</span>
              <Badge variant="default" className="bg-green-600 ml-auto">
                {contact.status_display || "Active"}
              </Badge>
            </div>
          </div>
        </div>
      </div>
      <div className="flex justify-center pt-2">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="w-8 h-8 rounded-full">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>Edit</DropdownMenuItem>
            <DropdownMenuItem>Delete</DropdownMenuItem>
            <DropdownMenuItem>View Details</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )



  return (
    <div className="min-h-screen bg-slate-900 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-white mb-2">Contact</h1>
        </div>
        <Button className="bg-red-600 hover:bg-red-700 text-white">
          <Plus className="h-4 w-4 mr-2" />
          Add Contact
        </Button>
      </div>

      {/* Search and Filters */}
      <div className="bg-slate-800 border border-slate-700 rounded-lg p-4 mb-6">
        <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center">
          {/* Search */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search Contacts"
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-gray-400"
            />
          </div>

          {/* Contact Group Selector */}
          <Select value={selectedListId} onValueChange={setSelectedListId}>
            <SelectTrigger className="w-full lg:w-48 bg-slate-700 border-slate-600 text-white">
              <SelectValue placeholder="Choose a contact upload csv file" />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700">
              {Array.isArray(contactGroups) && contactGroups.map((group) => (
                <SelectItem key={group.list_id} value={group.list_id} className="text-white">
                  {group.group_name}
                </SelectItem>
              ))}
              {(!Array.isArray(contactGroups) || contactGroups.length === 0) && (
                <SelectItem value="no-groups" disabled className="text-gray-400">
                  No contact groups available
                </SelectItem>
              )}
            </SelectContent>
          </Select>

          {/* Browse Button */}
          <Button variant="outline" className="bg-slate-700 border-slate-600 text-white hover:bg-slate-600">
            Browse
          </Button>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button className="bg-green-600 hover:bg-green-700 text-white">
              <Upload className="h-4 w-4" />
            </Button>
            <Button className="bg-orange-600 hover:bg-orange-700 text-white">
              <Download className="h-4 w-4" />
            </Button>
            <Button className="bg-red-600 hover:bg-red-700 text-white">
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>

          {/* Add Contact Button */}
          <Button className="bg-blue-600 hover:bg-blue-700 text-white">
            + Add Contact
          </Button>
        </div>
      </div>

      {/* Contact Table */}
      <div className="bg-slate-800 border border-slate-700 rounded-lg">
        <DataTable
          data={contacts}
          columns={columns}
          searchable={false} // We handle search above
          onSearch={handleSearch}
          onExport={handleExport}
          mobileCardRender={mobileCardRender}
          totalCount={totalCount}
          currentPage={currentPage}
          loading={loading}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          pageSize={pageSize}
          pageSizes={[10, 20, 50, 100]}
          className="bg-slate-800"
        />
      </div>
    </div>
  )
}

export default ContactView