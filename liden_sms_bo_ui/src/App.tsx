import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { DashboardLayout } from "@/components/DashboardLayout";
import { AuthProvider } from "@/components/AuthProvider";
import { ProtectedRoute } from "@/components/ProtectedRoute";
import Dashboard from "./pages/dashboard/Dashboard";
import Analytics from "./pages/dashboard/Analytics";
import { ContactView } from "./pages/contact/ContactView";
import SMSBulkOutbox from "./pages/sms/SMSBulkOutbox";
import SMSBulkAnalytic from "./pages/sms/SMSBulkAnalytic";
import SMSBulkBlacklist from "./pages/sms/SMSBulkBlacklist";
import Message from "./pages/sms/Message";
import ContactGroups from "./pages/contact/ContactGroups";
import SenderIds from "./pages/settings/SenderIds";
import Login from "./pages/auth/Login";
import ForgotPassword from "./pages/auth/ForgotPassword";
import ResetPassword from "./pages/auth/ResetPassword";
import CampaignDetail from "./pages/CampaignDetail";
import NotFound from "./pages/NotFound";
// SMS Services
import SMSPremium from "./pages/sms/SMSPremium";
import SMSShortcode from "./pages/sms/SMSShortcode";
import SMSAlphanumeric from "./pages/sms/SMSAlphanumeric";
import Inbox from "./pages/sms/Inbox";
// Survey Pages
import SurveyStats from "./pages/survey/SurveyStats";
import CreateSurvey from "./pages/survey/CreateSurvey";
// Other Services
import USSD from "./pages/USSD";
import Airtime from "./pages/Airtime";
import Billing from "./pages/Billing";
// Settings Pages
import MyRequest from "./pages/settings/MyRequest";
import ApplyRequest from "./pages/settings/ApplyRequest";
import BulkRateCard from "./pages/bulkrate/BulkRateCard";
import Invoice from "./pages/invoice/Invoice";
import UserManagement from "./pages/user/UserView";
import UserDetails from "./pages/user/UserDetails";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
        <AuthProvider>
          <Routes>
            {/* Public Routes */}
            <Route path="/login" element={<Login />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/reset-password" element={<ResetPassword />} />

            {/* Protected Routes */}
            <Route path="/" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <Dashboard />
                </DashboardLayout>
              </ProtectedRoute>
            } />
            <Route path="/analytics" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <Analytics />
                </DashboardLayout>
              </ProtectedRoute>
            } />
            <Route path="/contact/view" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <ContactView />
                </DashboardLayout>
              </ProtectedRoute>
            } />
            <Route path="/contact/groups" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <ContactGroups />
                </DashboardLayout>
              </ProtectedRoute>
            } />

            {/* SMS Bulk Routes */}
            <Route path="/sms/bulk/outbox" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <SMSBulkOutbox />
                </DashboardLayout>
              </ProtectedRoute>
            } />
            <Route path="/sms/bulk/analytic" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <SMSBulkAnalytic />
                </DashboardLayout>
              </ProtectedRoute>
            } />
            <Route path="/sms/bulk/message" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <Message />
                </DashboardLayout>
              </ProtectedRoute>
            } />
            <Route path="/sms/bulk/blacklist" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <SMSBulkBlacklist />
                </DashboardLayout>
              </ProtectedRoute>
            } />

            {/* SMS Service Routes */}
            <Route path="/sms" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <SMSBulkOutbox />
                </DashboardLayout>
              </ProtectedRoute>
            } />
            <Route path="/sms/bulk" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <SMSBulkOutbox />
                </DashboardLayout>
              </ProtectedRoute>
            } />
            <Route path="/sms/premium" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <SMSPremium />
                </DashboardLayout>
              </ProtectedRoute>
            } />
            <Route path="/sms/shortcode" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <SMSShortcode />
                </DashboardLayout>
              </ProtectedRoute>
            } />
            <Route path="/sms/alphanumeric" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <SMSAlphanumeric />
                </DashboardLayout>
              </ProtectedRoute>
            } />
            <Route path="/inbox" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <Inbox />
                </DashboardLayout>
              </ProtectedRoute>
            } />

            {/* Contact Routes */}
            <Route path="/contact" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <ContactView />
                </DashboardLayout>
              </ProtectedRoute>
            } />

            {/* Survey Routes */}
            <Route path="/survey" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <SurveyStats />
                </DashboardLayout>
              </ProtectedRoute>
            } />
            <Route path="/survey/stats" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <SurveyStats />
                </DashboardLayout>
              </ProtectedRoute>
            } />
            <Route path="/survey/create" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <CreateSurvey />
                </DashboardLayout>
              </ProtectedRoute>
            } />

            {/* Other Service Routes */}
            <Route path="/ussd" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <USSD />
                </DashboardLayout>
              </ProtectedRoute>
            } />
            <Route path="/airtime" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <Airtime />
                </DashboardLayout>
              </ProtectedRoute>
            } />
            <Route path="/billing" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <Billing />
                </DashboardLayout>
              </ProtectedRoute>
            } />

            {/* Settings Routes */}
            <Route path="/settings/services-request/my-request" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <MyRequest />
                </DashboardLayout>
              </ProtectedRoute>
            } />
            <Route path="/settings/services-request/apply-request" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <ApplyRequest />
                </DashboardLayout>
              </ProtectedRoute>
            } />
            {/* Redirect old services-request route to my-request */}
            <Route path="/settings/services-request" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <MyRequest />
                </DashboardLayout>
              </ProtectedRoute>
            } />
            <Route path="/settings/bulk-rate-card" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <BulkRateCard />
                </DashboardLayout>
              </ProtectedRoute>
            } />
            <Route path="/settings/invoice" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <Invoice />
                </DashboardLayout>
              </ProtectedRoute>
            } />
            <Route path="/settings/user-management" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <UserManagement />
                </DashboardLayout>
              </ProtectedRoute>
            } />
            <Route path="/settings/user-management/:userId" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <UserDetails />
                </DashboardLayout>
              </ProtectedRoute>
            } />
            <Route path="/settings/sender-ids" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <SenderIds />
                </DashboardLayout>
              </ProtectedRoute>
            } />

            <Route path="/campaign/:campaignId" element={
              <ProtectedRoute>
                <CampaignDetail />
              </ProtectedRoute>
            } />

            {/* Catch-all route */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </AuthProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
